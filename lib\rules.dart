import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:smsautoforwardapp/bottomsheet_rule.dart';
import 'package:smsautoforwardapp/controller/rules_controller.dart';
import 'package:smsautoforwardapp/style.dart';

import 'controller/auth_controller.dart';

import 'model/rule_model.dart';

class ManageRules extends StatefulWidget {
  const ManageRules({super.key});

  @override
  State<ManageRules> createState() => _ManageRulesState();
}

class _ManageRulesState extends State<ManageRules> {
  final AuthController authController = Get.find();
  final RulesController rulesController = RulesController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: bgColor,
        resizeToAvoidBottomInset: false,
        body: SafeArea(
            child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(children: [
            // SizedBox(
            //   height: Get.height * 0.0,
            // ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Card(
                  elevation: 1,
                  shape: const CircleBorder(),
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(5.0),
                      child: InkWell(
                        onTap: () {
                          Get.back();
                        },
                        child: const Icon(
                          Icons.arrow_back,
                          color: blackishColor,
                        ),
                      ),
                    ),
                  ),
                ),
                txt(
                    txt: 'Manage Rules',
                    fontWeight: FontWeight.bold,
                    fontColor: blackishColor,
                    fontSize: 26),
                IconButton(
                  onPressed: () {
                    bottomSheetRule(context, null);
                  },
                  icon: const Icon(Icons.add_box_outlined),
                )
              ],
            ),
            SizedBox(
              height: Get.height * 0.03,
            ),
            Expanded(
                child: StreamBuilder<QuerySnapshot<Map<String, dynamic>>>(
              stream: FirebaseFirestore.instance
                  .collection('users')
                  .doc(authController.user!.uid)
                  .collection('rules')
                  .snapshots(),
              builder: (context, snapshot) {
                if (snapshot.hasError) {
                  return Container();
                }

                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                final documents = snapshot.data?.docs ?? [];

                if (documents.isEmpty) {
                  return Center(
                    child: ElevatedButton(
                      onPressed: () {
                        bottomSheetRule(context, null);
                      },
                      child: txt(
                        txt: 'Make new rules',
                        fontSize: 16,
                        fontColor: Colors.white,
                      ),
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: documents.length,
                  itemBuilder: (context, index) {
                    final docData = documents[index].data();
                    final rule = RuleModelClass.fromJson(docData);

                    // Now you can use the 'rule' object to display the data in your ListView.

                    return Card(
                      elevation: 0,
                      child: ListTile(
                        onTap: () {
                          bottomSheetRule(context, rule);
                        },
                        trailing:
                            Row(mainAxisSize: MainAxisSize.min, children: [
                          Switch(
                            value: rule.isActive,
                            onChanged: (bool value) {
                              rulesController.updateRuleActiveStatus(
                                  rule: rule, value: value);
                            },
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          IconButton(
                            onPressed: () {
                              FirebaseFirestore.instance
                                  .collection('users')
                                  .doc(authController.user!.uid)
                                  .collection('rules')
                                  .doc(rule
                                      .ruleID) // Assuming 'id' is the unique identifier of the rule
                                  .delete()
                                  .then((value) {
                                // Rule deleted successfully
                              }).catchError((error) {
                                // An error occurred while deleting the rule
                                // You can handle the error here, e.g., show an error message.
                              });
                            },
                            icon: const Icon(
                              Icons.delete,
                            ),
                          ),
                        ]),
                        title: Row(
                          children: [
                            const Icon(Icons.rule),
                            const SizedBox(
                              width: 8,
                            ),
                            Expanded(
                                child: txt(txt: rule.ruleName, fontSize: 18)),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ))
          ]),
        )));
  }
}
