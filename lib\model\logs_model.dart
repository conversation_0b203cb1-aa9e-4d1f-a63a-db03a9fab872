import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

class LogModelClass {
  String logTitle;
  String ruleName;
  List<String> keywords;
  String senderNumber;
  String receiver;
  String additionalMessage;
  String message;
  bool isEmail;
  bool status;
  DateTime dateTime;
  String httpMethod;
  String url;
  String jsonBody;

  LogModelClass({
    required this.logTitle,
    required this.additionalMessage,
    required this.isEmail,
    required this.ruleName,
    required this.keywords,
    required this.senderNumber,
    required this.receiver,
    required this.message,
    required this.status,
    required this.dateTime,
    required this.httpMethod,
    required this.url,
    required this.jsonBody,
  });

  factory LogModelClass.fromJson(Map<String, dynamic> json) {
    DateTime parsedDateTime =
        DateFormat("yyyy-MM-dd HH:mm:ss").parse(json['dateTime']);
    return LogModelClass(
      logTitle: json['logTitle'],
      isEmail: json['isEmail'],
      additionalMessage: json['additionalMessage'],
      ruleName: json['ruleName'],
      keywords: List<String>.from(json['keywords']),
      senderNumber: json['senderNumber'],
      receiver: json['receiver'],
      message: json['message'],
      status: json['status'],
      dateTime: parsedDateTime,
      httpMethod: json['httpMethod'],
      url: json['url'],
      jsonBody: json['jsonBody'],
    );
  }

  Map<String, dynamic> toJson() => {
        "logTitle": logTitle,
        "ruleName": ruleName,
        "isEmail": isEmail,
        "additionalMessage": additionalMessage,
        "keywords": keywords,
        "senderNumber": senderNumber,
        "receiver": receiver,
        "message": message,
        "status": status,
        "dateTime": DateFormat("yyyy-MM-dd HH:mm:ss").format(dateTime),
        "httpMethod": httpMethod,
        "url": url,
        "jsonBody": jsonBody,
      };

  static LogModelClass fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;

    // Parse the dateTime string into a DateTime object
    DateTime parsedDateTime =
        DateFormat("yyyy-MM-dd HH:mm:ss").parse(snapshot['dateTime']);
    return LogModelClass(
      logTitle: snapshot['logTitle'],
      isEmail: snapshot['isEmail'],
      ruleName: snapshot['ruleName'],
      additionalMessage: snapshot['additionalMessage'],
      keywords: List<String>.from(snapshot['keywords']),
      senderNumber: snapshot['senderNumber'],
      receiver: snapshot['receiver'],
      message: snapshot['message'],
      status: snapshot['status'],
      dateTime: parsedDateTime,
      httpMethod: snapshot['httpMethod'],
      url: snapshot['url'],
      jsonBody: snapshot['jsonBody'],
    );
  }
}
