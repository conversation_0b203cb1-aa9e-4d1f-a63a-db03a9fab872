import 'package:cloud_firestore/cloud_firestore.dart';

class ForwaredAllEmailModelClass {
  bool isActive;

  List<String> recipients;

  ForwaredAllEmailModelClass({
    required this.isActive,
    required this.recipients,
  });

  factory ForwaredAllEmailModelClass.fromJson(Map<String, dynamic> json) {
    return ForwaredAllEmailModelClass(
      isActive: json['isActive'],
      recipients: List<String>.from(json['recipients']),
    );
  }

  Map<String, dynamic> toJson() => {
        "isActive": isActive,
        "recipients": recipients,
      };

  static ForwaredAllEmailModelClass fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return ForwaredAllEmailModelClass(
      isActive: snapshot['isActive'],
      recipients: snapshot['recipients'],
    );
  }
}
