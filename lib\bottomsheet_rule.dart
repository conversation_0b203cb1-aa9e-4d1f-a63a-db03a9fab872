import 'package:flutter/material.dart' hide ModalBottomSheetRoute;
import 'package:get/get.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:smsautoforwardapp/model/rule_model.dart';
import 'package:smsautoforwardapp/style.dart';

import 'bottomsheet_email.dart';
import 'controller/rules_controller.dart';

Future<dynamic> bottomSheetRule(BuildContext context, RuleModelClass? rule) {
  return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => PopScope(
            onPopInvokedWithResult: (didPop, result) async {
              await showDiscardChangesDialog(context);
            },
            child: Padding(
                padding: MediaQuery.of(context).viewInsets,
                child: SingleChildScrollView(
                    controller: ModalScrollController.of(context),
                    child: MyBottomSheetRule(
                      rule: rule,
                    ))),
          ));
}

class MyBottomSheetRule extends StatefulWidget {
  final RuleModelClass? rule;

  const MyBottomSheetRule({super.key, this.rule});

  @override
  State<MyBottomSheetRule> createState() => _MyBottomSheetRuleState();
}

class _MyBottomSheetRuleState extends State<MyBottomSheetRule> {
  String selectedDropdownValue = 'Email';
  String selectedHttpMethod = 'GET';

  bool isActive = true;
  bool isCaseSensitive = false;

  TextEditingController nameController = TextEditingController();
  TextEditingController numberController = TextEditingController();
  TextEditingController keywordsController = TextEditingController();
  TextEditingController emailRecipientsController = TextEditingController();
  TextEditingController urlController = TextEditingController();
  TextEditingController jsonBodyController = TextEditingController();
  final RulesController rulesController = RulesController();
  String nameError = '';
  String numberError = '';
  String emailRecipientsError = '';
  String urlError = '';
  String jsonBodyError = '';
  @override
  void dispose() {
    nameController.dispose();
    numberController.dispose();
    keywordsController.dispose();
    emailRecipientsController.dispose();
    urlController.dispose();
    jsonBodyController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    if (widget.rule != null) {
      // Replace brackets in keywords and recipients with an empty string

      String cleanedKeywords =
          widget.rule!.keywords.toString().replaceAll(RegExp(r'[^\w\s,]'), '');
      String cleanedRecipients = widget.rule!.recipients
          .toString()
          .replaceAll(RegExp(r'[^\w\s@.,]'), '');

// Now you can use keywordsList and recipientsList in your code
      selectedDropdownValue = widget.rule!.isEmail ? 'Email' : 'URL';
      isCaseSensitive = widget.rule!.isCaseSensitive ? true : false;
      isActive = widget.rule!.isActive ? true : false;
      nameController.text = widget.rule!.ruleName;
      numberController.text = widget.rule!.fromNumber;
      keywordsController.text = cleanedKeywords;

      if (widget.rule!.isEmail) {
        emailRecipientsController.text = cleanedRecipients;
      } else {
        urlController.text = widget.rule!.url;
        selectedHttpMethod = widget.rule!.method;
        jsonBodyController.text = widget.rule!.jsonBody;
      }
    } else {
      jsonBodyController.text =
          '{"text": "New message from {sender}:{body}"\n}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Get.height * 0.9,
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
      child: ListView(
        shrinkWrap: true,
        children: [
          Row(
            children: [
              txt(txt: 'Active', fontSize: 16),
              const Spacer(),
              Switch(
                value: isActive,
                onChanged: (bool value) {
                  setState(() {
                    isActive = value;
                  });
                },
              ),
            ],
          ),
          Card(
            elevation: 4.0,
            child: TextFormField(
              controller: nameController,
              decoration: InputDecoration(
                labelText: 'Name',
                border: InputBorder.none,
                errorText: nameError.isNotEmpty ? nameError : null,
              ),
              onChanged: (value) {
                setState(() {
                  nameError = ''; // Clear the error message
                });
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 10, bottom: 10),
            child: Container(
                decoration: BoxDecoration(
                    border: Border.all(color: maincolor, width: 1.5)),
                padding: const EdgeInsets.all(10.0),
                child: Column(children: [
                  Row(children: [
                    txt(txt: 'From', fontSize: 16, fontColor: maincolor),
                  ]),
                  const SizedBox(
                    height: 10,
                  ),
                  txt(
                      txt: 'Please include country code +(country code)',
                      fontSize: 12,
                      maxLines: 2,
                      fontStyle: FontStyle.italic,
                      fontColor: maincolor),
                  Card(
                    elevation: 4.0,
                    child: TextFormField(
                      controller: numberController,
                      decoration: InputDecoration(
                        labelText: 'Number',
                        hintText: '+12334567890',
                        errorText: numberError.isNotEmpty ? numberError : null,
                      ),
                      onChanged: (value) {
                        setState(() {
                          numberError = ''; // Clear the error message
                        });
                      },
                    ),
                  ),
                  Card(
                    elevation: 4.0,
                    child: TextFormField(
                      controller: keywordsController,
                      decoration: const InputDecoration(
                        labelText: 'Keywords',
                        hintText: 'fun,party',
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                  Row(
                    children: [
                      txt(txt: 'Case Sensitive', fontSize: 16),
                      const Spacer(),
                      Switch(
                        value: isCaseSensitive,
                        onChanged: (bool value) {
                          setState(() {
                            isCaseSensitive = value;
                          });
                        },
                      ),
                    ],
                  ),
                ])),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 10, bottom: 10),
            child: Container(
                decoration: BoxDecoration(
                    border: Border.all(width: 1.5, color: maincolor)),
                padding: const EdgeInsets.all(10.0),
                child: Column(children: [
                  Row(children: [
                    txt(txt: 'To', fontSize: 16, fontColor: maincolor),
                  ]),
                  const SizedBox(
                    height: 10,
                  ),
                  Card(
                    elevation: 4.0,
                    child: InputDecorator(
                      decoration:
                          const InputDecoration(border: OutlineInputBorder()),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          isExpanded: true,
                          dropdownColor: Colors.white,
                          borderRadius: BorderRadius.zero,
                          value: selectedDropdownValue,
                          underline: Container(),
                          onChanged: (String? newValue) {
                            setState(() {
                              selectedDropdownValue = newValue!;
                            });
                          },
                          items: <String>['Email', 'URL'].map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: txt(txt: value, fontSize: 18),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),
                  if (selectedDropdownValue == 'Email')
                    Card(
                      elevation: 4.0,
                      child: TextFormField(
                        controller: emailRecipientsController,
                        decoration: InputDecoration(
                          labelText: 'Recipients',
                          border: InputBorder.none,
                          hintText: '<EMAIL>, <EMAIL>',
                          errorText: emailRecipientsError.isNotEmpty
                              ? emailRecipientsError
                              : null,
                        ),
                        onChanged: (value) {
                          setState(() {
                            emailRecipientsError =
                                ''; // Clear the error message
                          });
                        },
                      ),
                    )
                  else if (selectedDropdownValue == 'URL')
                    Column(
                      children: [
                        Card(
                          elevation: 4.0,
                          child: InputDecorator(
                            decoration: const InputDecoration(
                                border: OutlineInputBorder()),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<String>(
                                value: selectedHttpMethod,
                                isExpanded: true,
                                dropdownColor: Colors.white,
                                underline: Container(),
                                onChanged: (String? newValue) {
                                  setState(() {
                                    selectedHttpMethod = newValue!;
                                  });
                                },
                                items: <String>[
                                  'GET',
                                  'POST',
                                  'PUT',
                                  'PATCH',
                                  'DELETE'
                                ].map((String value) {
                                  return DropdownMenuItem<String>(
                                    value: value,
                                    child: txt(txt: value, fontSize: 18),
                                  );
                                }).toList(),
                              ),
                            ),
                          ),
                        ),
                        Card(
                          elevation: 4.0,
                          child: TextFormField(
                            controller: urlController,
                            decoration: InputDecoration(
                              labelText: 'URL',
                              hintText: 'http://example.com/message',
                              border: InputBorder.none,
                              errorText: urlError.isNotEmpty ? urlError : null,
                            ),
                            onChanged: (value) {
                              setState(() {
                                urlError = ''; // Clear the error message
                              });
                            },
                          ),
                        ),
                        selectedHttpMethod == 'GET'
                            ? const SizedBox.shrink()
                            : Card(
                                elevation: 4.0,
                                child: TextFormField(
                                  maxLines: 5,
                                  controller: jsonBodyController,
                                  decoration: InputDecoration(
                                    labelText: 'JSON BODY',
                                    hintText:
                                        '{\n"text": "New message from {sender}:{body}"\n}',
                                    border: InputBorder.none,
                                    errorText: jsonBodyError.isNotEmpty
                                        ? jsonBodyError
                                        : null,
                                  ),
                                  onChanged: (value) {
                                    setState(() {
                                      jsonBodyError =
                                          ''; // Clear the error message
                                    });
                                  },
                                ),
                              ),
                      ],
                    ),
                ])),
          ),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                // Replace brackets in keywords and recipients with an empty string
                String cleanedKeywords =
                    keywordsController.text.replaceAll(RegExp(r'[^\w\s,]'), '');
                String cleanedRecipients = emailRecipientsController.text
                    .replaceAll(RegExp(r'[^\w\s@.,]'), '');

// Split the cleaned keywords and recipients by comma and trim whitespace
                List<String> keywordsList =
                    cleanedKeywords.split(',').map((e) => e.trim()).toList();
                List<String> recipientsList =
                    cleanedRecipients.split(',').map((e) => e.trim()).toList();

                // Check if any of the required fields are empty
                if (nameController.text.isEmpty) {
                  setState(() {
                    nameError = 'Name cannot be empty';
                  });
                  return;
                } else if (numberController.text.isEmpty) {
                  setState(() {
                    numberError = 'Number cannot be empty';
                  });
                  return;
                } else {
                  if (selectedDropdownValue == 'Email') {
                    if (emailRecipientsController.text.isEmpty) {
                      setState(() {
                        emailRecipientsError = 'Recipients cannot be empty';
                      });
                      return;
                    } else {
                      Get.back();
                      if (widget.rule == null) {
                        rulesController.addNewRuleInList(RuleModelClass(
                            ruleID: '',
                            isActive: isActive,
                            isCaseSensitive: isCaseSensitive,
                            ruleName: nameController.text,
                            fromNumber: numberController.text,
                            keywords: keywordsList,
                            isEmail: true,
                            recipients: recipientsList,
                            method: '',
                            url: '',
                            jsonBody: ''));
                      } else {
                        rulesController.updateRuleInList(RuleModelClass(
                            ruleID: widget.rule!.ruleID,
                            isActive: isActive,
                            isCaseSensitive: isCaseSensitive,
                            ruleName: nameController.text,
                            fromNumber: numberController.text,
                            keywords: keywordsList,
                            isEmail: true,
                            recipients: recipientsList,
                            method: '',
                            url: '',
                            jsonBody: ''));
                      }
                    }
                  } else if (selectedDropdownValue == 'URL') {
                    if (selectedHttpMethod != 'GET') {
                      if (urlController.text.isEmpty ||
                          jsonBodyController.text.isEmpty) {
                        if (urlController.text.isEmpty) {
                          setState(() {
                            urlError = 'URL cannot be empty';
                          });
                          return;
                        }
                        if (jsonBodyController.text.isEmpty) {
                          setState(() {
                            jsonBodyError = 'JSON BODY cannot be empty';
                          });
                          return;
                        }
                      } else {
                        Get.back();
                        if (widget.rule == null) {
                          rulesController.addNewRuleInList(RuleModelClass(
                              isActive: isActive,
                              ruleID: '',
                              isCaseSensitive: isCaseSensitive,
                              ruleName: nameController.text,
                              fromNumber: numberController.text,
                              keywords: keywordsList,
                              isEmail: false,
                              recipients: [],
                              method: selectedHttpMethod,
                              url: urlController.text,
                              jsonBody: jsonBodyController.text));
                        } else {
                          rulesController.updateRuleInList(RuleModelClass(
                              ruleID: widget.rule!.ruleID,
                              isActive: isActive,
                              isCaseSensitive: isCaseSensitive,
                              ruleName: nameController.text,
                              fromNumber: numberController.text,
                              keywords: keywordsList,
                              isEmail: false,
                              recipients: [],
                              method: selectedHttpMethod,
                              url: urlController.text,
                              jsonBody: jsonBodyController.text));
                        }
                      }
                    } else {
                      if (urlController.text.isEmpty) {
                        setState(() {
                          urlError = 'URL cannot be empty';
                        });
                        return;
                      } else {
                        Get.back();
                        if (widget.rule == null) {
                          rulesController.addNewRuleInList(RuleModelClass(
                              isActive: isActive,
                              ruleID: '',
                              isCaseSensitive: isCaseSensitive,
                              ruleName: nameController.text,
                              fromNumber: numberController.text,
                              keywords: keywordsList,
                              isEmail: false,
                              recipients: [],
                              method: selectedHttpMethod,
                              url: urlController.text,
                              jsonBody: jsonBodyController.text));
                        } else {
                          rulesController.updateRuleInList(RuleModelClass(
                              ruleID: widget.rule!.ruleID,
                              isActive: isActive,
                              isCaseSensitive: isCaseSensitive,
                              ruleName: nameController.text,
                              fromNumber: numberController.text,
                              keywords: keywordsList,
                              isEmail: false,
                              recipients: [],
                              method: selectedHttpMethod,
                              url: urlController.text,
                              jsonBody: jsonBodyController.text));
                        }
                      }
                    }
                  }
                }
              },
              child: txt(txt: 'Apply', fontSize: 16, fontColor: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
