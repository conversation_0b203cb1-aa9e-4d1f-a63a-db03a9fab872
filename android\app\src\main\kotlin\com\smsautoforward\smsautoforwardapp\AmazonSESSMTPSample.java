package com.smsautoforward.smsautoforwardapp;

import android.content.Context;
import android.util.Log;

import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailService;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClient;
import com.amazonaws.services.simpleemail.model.Body;
import com.amazonaws.services.simpleemail.model.Content;
import com.amazonaws.services.simpleemail.model.Destination;
import com.amazonaws.services.simpleemail.model.Message;
import com.amazonaws.services.simpleemail.model.SendEmailRequest;
import com.google.firebase.firestore.DocumentSnapshot;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.QuerySnapshot;

import java.util.Map;

public class AmazonSESSMTPSample {

    public void sendEmail(Context context, String recipient, String subject, String message) {
        String collectionName = "keys";
        String documentId = "EpeCvvUplL10z64pEryP";

        FirebaseFirestore.getInstance()
                .collection(collectionName)
                .document(documentId)
                .get()
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        handleDocumentSnapshot(task.getResult(), recipient, subject, message);
                    } else {
                        // Handle errors
                        Log.e("EmailSender", "Error getting document: " + task.getException());
                        // TODO: Handle the exception
                    }
                });
    }

    private void handleDocumentSnapshot(DocumentSnapshot documentSnapshot, String recipient, String subject, String message) {
        if (documentSnapshot != null && documentSnapshot.exists()) {
            // Handle the rule document here
            Object data = documentSnapshot.getData();
            // TODO: Do something with 'data'

            if (data != null) {
                String accessKey = (String) ((Map<?, ?>) data).get("AWS_ACCESS_KEY");
                String secretKey = (String) ((Map<?, ?>) data).get("AWS_SECRET_KEY");

                if (accessKey != null && secretKey != null) {
                new Thread(() -> {
                    sendEmailWithAWS(accessKey, secretKey, recipient, subject, message);
                }).start();


                } else {
                    Log.e("EmailSender", "AWS_ACCESS_KEY or AWS_SECRET_KEY is null");
                }
            }
        } else {
            // Document does not exist
            Log.d("EmailSender", "Document does not exist");
            // TODO: Handle the case where the document does not exist
        }
    }

    private void sendEmailWithAWS(String accessKey, String secretKey, String recipient, String subject, String message) {
        // Create BasicAWSCredentials using your access and secret keys
        BasicAWSCredentials awsCredentials = new BasicAWSCredentials(accessKey, secretKey);

        try {
            // Create the Amazon SES client and set credentials
            AmazonSimpleEmailService client = new AmazonSimpleEmailServiceClient(awsCredentials);

            // Set the AWS region (eu-north-1 in this case)
            client.setRegion(Region.getRegion(Regions.EU_NORTH_1));

            // Replace with your email details
            SendEmailRequest request = new SendEmailRequest()
                    .withDestination(new Destination().withToAddresses(recipient))
                    .withMessage(new Message()
                            .withBody(new Body()
                                    .withText(new Content()
                                            .withCharset("UTF-8").withData(message)))
                            .withSubject(new Content()
                                    .withCharset("UTF-8").withData(subject)))
                    .withSource("<EMAIL>");

            client.sendEmail(request);
            Log.d("EmailSender", "Email sent!");
        } catch (Exception ex) {
            Log.e("EmailSender", "The email was not sent. Error message: " + ex.getMessage(), ex);
        }
    }
}
