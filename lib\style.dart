import 'package:auto_size_text/auto_size_text.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

const MaterialColor myCustomPrimarySwatch = MaterialColor(
  0xff29ABE2,
  <int, Color>{
    50: Color(0xff29ABE2),
    100: Color(0xff29ABE2),
    200: Color(0xff29ABE2),
    300: Color(0xff29ABE2),
    400: Color(0xff29ABE2),
    500: Color(0xff29ABE2),
    600: Color(0xff29ABE2),
    700: Color(0xff29ABE2),
    800: Color(0xff29ABE2),
    900: Color(0xff29ABE2),
  },
);

const Color maincolor = Color(0xff29ABE2);
const Color bgColor = Color(0xffF8F7FC);
const Color blackishColor = Color(0xff1F232F);
const Color lightGrayColor = Color(0xff1F232F);

// FIREBASE
var firebaseAuth = FirebaseAuth.instance;
var firestore = FirebaseFirestore.instance;

Widget appName = Row(
  mainAxisAlignment: MainAxisAlignment.center,
  children: [
    SvgPicture.asset(
      'assets/logo.svg',
      height: Get.height * 0.03,
    ),
    const SizedBox(
      width: 3,
    ),
    txt(
      txt: 'SMS',
      fontColor: blackishColor,
      maxLines: 2,
      fontWeight: FontWeight.w400,
      textAlign: TextAlign.center,
      fontSize: 26,
    ),
    txt(
      txt: 'AutoForwarder',
      fontColor: maincolor,
      maxLines: 2,
      fontWeight: FontWeight.bold,
      textAlign: TextAlign.center,
      fontSize: 26,
    ),
  ],
);

//for all the text in the app
Widget txt(
    {required String txt,
    FontWeight? fontWeight,
    FontStyle? fontStyle,
    required double fontSize,
    Color? fontColor,
    Color? bgColor,
    double? minFontSize,
    double? letterSpacing,
    TextOverflow? overflow,
    TextAlign? textAlign,
    bool? isUnderline,
    String? font,
    int? maxLines}) {
  return AutoSizeText(txt,
      maxLines: maxLines ?? 1,
      maxFontSize: fontSize,
      minFontSize: minFontSize ?? fontSize - 10,
      textAlign: textAlign,
      style: GoogleFonts.raleway(
        textStyle: TextStyle(
          decoration: isUnderline == null
              ? TextDecoration.none
              : TextDecoration.underline,
          fontStyle: fontStyle ?? FontStyle.normal,
          overflow: overflow ?? TextOverflow.ellipsis,
          letterSpacing: letterSpacing ?? 0,
          backgroundColor: bgColor,
          color: fontColor ?? blackishColor,
          fontWeight: fontWeight ?? FontWeight.w600,
        ),
      ));
}

Widget buttonContainer(BuildContext context,
    {double? width,
    Color? color,
    String? text,
    Widget? widget,
    bool? isSharpBorders,
    VoidCallback? onTap}) {
  return ElevatedButton(
    onPressed: onTap,
    style: ElevatedButton.styleFrom(
      backgroundColor: maincolor,
      shape: isSharpBorders != null
          ? RoundedRectangleBorder(borderRadius: BorderRadius.circular(0.0))
          : RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.0)),
    ),
    child: widget ??
        Center(child: txt(txt: text!, fontSize: 14, fontColor: Colors.white)),
  );
}

Widget whiteContainer(
  BuildContext context, {
  double? height,
  double? width,
  Color? color,
  Color? borderColor,
  String? text,
  Widget? child,
  double? borderWidth,
  bool? isSharpBorders,
  bool? isThereBorders,
}) {
  return Container(
      width: width ?? Get.width * 0.9,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15.0),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.16),
            offset: const Offset(0, 3.0),
            blurRadius: 6.0,
          ),
        ],
      ),
      child: child);
}

Widget textFieldContainer(BuildContext context,
    {TextEditingController? controller,
    String? hint,
    bool? isObscure,
    bool? isEnabled,
    bool? isAutoFocus,
    double? height,
    Widget? trailing,
    EdgeInsets? padding,
    FocusNode? focusNode,
    Widget? prefix,
    Function(String)? onChanged,
    String? Function(String?)? validator,
    bool? isOnlyNumberField,
    bool? isTextAlignCenter,
    int? maxLines}) {
  return Container(
    height: height ?? Get.height * 0.05,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(10.0),
      color: const Color(0xFFF8F8F8),
    ),
    child: TextFormField(
      textAlignVertical: TextAlignVertical.center,
      textAlign: isTextAlignCenter != null ? TextAlign.center : TextAlign.left,
      focusNode: focusNode,
      enabled: isEnabled == null
          ? true
          : isEnabled
              ? true
              : false,
      keyboardType:
          isOnlyNumberField == null ? TextInputType.text : TextInputType.number,
      obscureText: isObscure == null ? false : true,
      onChanged: onChanged,
      autofocus: isAutoFocus ?? false,
      maxLines: maxLines ?? 1,
      validator: validator ??
          (val) {
            if (val!.isEmpty) {
              return 'This field is required';
            } else {
              return null;
            }
          },
      style: GoogleFonts.montserrat(
        textStyle: const TextStyle(
          overflow: TextOverflow.ellipsis,
          color: maincolor,
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
      ),
      controller: controller,
      decoration: InputDecoration(
        contentPadding: padding ?? const EdgeInsets.all(0),
        isDense: false,
        suffixIcon: trailing,
        prefixIcon: prefix,
        border: InputBorder.none,
        hintText: hint,
        hintStyle: GoogleFonts.montserrat(
          textStyle: const TextStyle(
            overflow: TextOverflow.ellipsis,
            color: Colors.grey,
            fontSize: 14,
            fontWeight: FontWeight.normal,
          ),
        ),
      ),
    ),
  );
}

getErrorSnackBar(String message) {
  Get.snackbar(
    'Error',
    message,
    titleText: txt(
        txt: 'Error',
        fontSize: 22,
        fontColor: Colors.white,
        fontWeight: FontWeight.bold),
    messageText:
        txt(txt: message, fontSize: 14, maxLines: 2, fontColor: Colors.white),
    snackPosition: SnackPosition.BOTTOM,
    duration: const Duration(seconds: 2),
    backgroundColor: Colors.red.shade300,
    borderRadius: 0,
    margin: const EdgeInsets.only(bottom: 0, left: 0, right: 0),
  );
}

getWarningSnackBar(String message, {int? seconds}) {
  Get.snackbar(
    'Warning',
    message,
    titleText: txt(
        txt: 'Warning',
        fontSize: 22,
        fontColor: Colors.black54,
        fontWeight: FontWeight.bold),
    messageText: txt(txt: message, fontSize: 14, fontColor: Colors.black54),
    snackPosition: SnackPosition.BOTTOM,
    duration: Duration(seconds: seconds ?? 2),
    backgroundColor: Colors.yellow.shade300,
    borderRadius: 0,
    margin: const EdgeInsets.only(bottom: 0, left: 0, right: 0),
  );
}

getSuccessSnackBar(String message, {int? seconds}) {
  Get.snackbar(
    'Success',
    message,
    titleText: txt(
        txt: 'Success',
        fontSize: 22,
        fontColor: Colors.white,
        fontWeight: FontWeight.bold),
    messageText: txt(txt: message, fontSize: 14, fontColor: Colors.white),
    snackPosition: SnackPosition.BOTTOM,
    duration: Duration(seconds: seconds ?? 2),
    backgroundColor: Colors.green.shade300,
    borderRadius: 0,
    margin: const EdgeInsets.only(bottom: 0, left: 0, right: 0),
  );
}
