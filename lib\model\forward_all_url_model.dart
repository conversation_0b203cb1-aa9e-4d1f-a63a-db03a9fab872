import 'package:cloud_firestore/cloud_firestore.dart';

class ForwaredAllURLModelClass {
  bool isActive;
  String method;
  String url;
  String jsonBody;

  ForwaredAllURLModelClass({
    required this.isActive,
    required this.method,
    required this.url,
    required this.jsonBody,
  });

  factory ForwaredAllURLModelClass.fromJson(Map<String, dynamic> json) {
    return ForwaredAllURLModelClass(
      isActive: json['isActive'],
      method: json['method'],
      url: json['url'],
      jsonBody: json['jsonBody'],
    );
  }

  Map<String, dynamic> toJson() => {
        "isActive": isActive,
        "url": url,
        "method": method,
        "jsonBody": jsonBody,
      };

  static ForwaredAllURLModelClass fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return ForwaredAllURLModelClass(
      isActive: snapshot['isActive'],
      method: snapshot['method'],
      url: snapshot['url'],
      jsonBody: snapshot['jsonBody'],
    );
  }
}
