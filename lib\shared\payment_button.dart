import 'package:flutter/material.dart';
import 'package:smsautoforwardapp/style.dart';

class PaymentButton extends StatelessWidget {
  final String buttonTitle;
  final Function()? onPressed;

  const PaymentButton({
    super.key,
    this.buttonTitle = 'Pay order',
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ButtonStyle(
        padding:
            WidgetStateProperty.all(const EdgeInsets.fromLTRB(10, 10, 10, 10)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.credit_card,
            color: Color(0xfff8f7ff),
          ),
          const SizedBox(width: 15),
          txt(txt: buttonTitle, fontSize: 15, fontColor: Colors.white)
        ],
      ),
    );
  }
}
