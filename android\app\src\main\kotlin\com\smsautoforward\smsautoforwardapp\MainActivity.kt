package com.smsautoforward.smsautoforwardapp

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.content.Intent 
import android.util.Log
import com.google.firebase.FirebaseApp
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.GeneratedPluginRegistrant

class MainActivity: FlutterFragmentActivity () {


    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        GeneratedPluginRegistrant.registerWith(flutterEngine)
        // Initialize Firebase (if not already initialized)
        FirebaseApp.initializeApp(this)


        // Register a method channel for starting and stopping the background service
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "smsAutoForwarderAppChannel")
    .setMethodCallHandler { call, result ->
        when (call.method) {
            "registerSmsBackgroundReceiver" -> {
                // The receiver is already registered in AndroidManifest.xml
                // This method can be used for any additional setup if needed
                Log.d(TAG, "SMS Background Receiver registration acknowledged")
                result.success(true)
            }
            "sendAuthStateToAndroid" -> {
                val uid = call.argument<String>("uid")
                val authToken = call.argument<String>("authToken")
                if (uid != null) {
                    val prefs: SharedPreferences = this.getSharedPreferences("UUIDSTRING", Context.MODE_PRIVATE)
                    prefs.edit().putString("UUIDSTRING", uid).apply()

                    // Store auth information using FirebaseAuthHelper
                    FirebaseAuthHelper.getInstance().storeAuthToken(this, uid, authToken)

                    val uiddd: String? = prefs.getString("UUIDSTRING", null)
                    Log.d(TAG, "Stored 'UUIDSTRING' in Android SharedPreferences: $uiddd")
                    result.success(true)
                } else {
                    result.error("UID_NULL", "UID is null or missing", null)
                }
            }
            "stopBackgroundService" -> {
                // Clear the UID from SharedPreferences to stop SMS processing
                Log.d(TAG, "'UUIDSTRING' deleting")
                val prefs: SharedPreferences = this.getSharedPreferences("UUIDSTRING", Context.MODE_PRIVATE)
                prefs.edit().remove("UUIDSTRING").apply()

                // Clear auth data using FirebaseAuthHelper
                FirebaseAuthHelper.getInstance().clearAuthData(this)

                Log.d(TAG, "Cleared 'UUIDSTRING' from Android SharedPreferences - SMS processing stopped")
                result.success(true)
            }
            else -> result.notImplemented()
        }
    }
    }


    companion object {
        private const val TAG = "MainActivity"
    }
}
