import 'package:flutter/material.dart' hide ModalBottomSheetRoute;
import 'package:get/get.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:smsautoforwardapp/controller/rules_controller.dart';
import 'package:smsautoforwardapp/model/forward_all_email_model.dart';
import 'package:smsautoforwardapp/style.dart';

Future<dynamic> bottomSheetEmail(BuildContext context,
    ForwaredAllEmailModelClass forwaredAllEmailModelClass) {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (context) => PopScope(
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final result = await showDiscardChangesDialog(context);
          if (result == true) {
            Get.back();
          }
        }
      },
      child: Padding(
        padding: MediaQuery.of(context).viewInsets,
        child: SingleChildScrollView(
          controller: ModalScrollController.of(context),
          child: MyBottomSheetEmail(
              forwaredAllEmailModelClass: forwaredAllEmailModelClass),
        ),
      ),
    ),
  );
}

class MyBottomSheetEmail extends StatefulWidget {
  final ForwaredAllEmailModelClass forwaredAllEmailModelClass;
  const MyBottomSheetEmail(
      {super.key, required this.forwaredAllEmailModelClass});

  @override
  State<MyBottomSheetEmail> createState() => _MyBottomSheetEmailState();
}

class _MyBottomSheetEmailState extends State<MyBottomSheetEmail> {
  bool isActive = true;
  final RulesController rulesController = RulesController();
  String emailRecipientsError = '';

  TextEditingController emailRecipientsController = TextEditingController();

  @override
  void dispose() {
    super.dispose();
    emailRecipientsController.dispose();
  }

  @override
  void initState() {
    super.initState();
    String cleanedRecipients = widget.forwaredAllEmailModelClass.recipients
        .toString()
        .replaceAll(RegExp(r'[^\w\s@.,]'), '');

    isActive = widget.forwaredAllEmailModelClass.isActive;

    emailRecipientsController.text = cleanedRecipients;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              txt(txt: 'Active', fontSize: 16),
              const Spacer(),
              Switch(
                value: isActive,
                onChanged: (bool value) {
                  setState(() {
                    isActive = value;
                  });
                },
              ),
            ],
          ),
          Card(
            elevation: 4.0,
            child: TextFormField(
              controller: emailRecipientsController,
              decoration: InputDecoration(
                labelText: 'Recipients',
                hintText: '<EMAIL>, <EMAIL>',
                border: InputBorder.none,
                errorText: emailRecipientsError.isNotEmpty
                    ? emailRecipientsError
                    : null,
              ),
              onChanged: (value) {
                setState(() {
                  emailRecipientsError = ''; // Clear the error message
                });
              },
            ),
          ),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                // Replace brackets in keywords and recipients with an empty string

                String cleanedRecipients = emailRecipientsController.text
                    .replaceAll(RegExp(r'[^\w\s@.,]'), '');

// Split the cleaned keywords and recipients by comma and trim whitespace

                List<String> recipientsList =
                    cleanedRecipients.split(',').map((e) => e.trim()).toList();

                // Check if any of the required fields ar e empty

                if (emailRecipientsController.text.isEmpty) {
                  setState(() {
                    emailRecipientsError = 'Recipients cannot be empty';
                  });
                  return;
                } else {
                  Get.back();
                  rulesController
                      .updateForwardAllEmail(ForwaredAllEmailModelClass(
                    isActive: isActive,
                    recipients: recipientsList,
                  ));
                }
              },
              child: txt(txt: 'Apply', fontSize: 16, fontColor: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}

showDiscardChangesDialog(BuildContext context) async {
  return showDialog<bool>(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        backgroundColor: Colors.white,
        title: const Text("Discard Changes?"),
        content:
            const Text("Do you really want to discard your pending changes?"),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(false); // User chose "No"
            },
            child: const Text("No"),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(true); // User chose "Yes"
            },
            child: const Text(
              "Yes",
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      );
    },
  );
}
