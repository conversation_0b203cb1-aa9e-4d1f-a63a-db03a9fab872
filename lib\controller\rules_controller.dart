import 'package:smsautoforwardapp/controller/auth_controller.dart';
import 'package:smsautoforwardapp/model/forward_all_email_model.dart';
import 'package:smsautoforwardapp/model/forward_all_url_model.dart';
import 'package:smsautoforwardapp/model/rule_model.dart';
import 'package:get/get.dart';

import '../style.dart';

class RulesController {
  final AuthController authController = Get.find();

  addNewRuleInList(RuleModelClass rule) async {
    try {
      await firestore
          .collection('users')
          .doc(authController.user!.uid)
          .collection('rules')
          .add(rule.toJson())
          .then((value) {
        firestore
            .collection('users')
            .doc(authController.user!.uid)
            .collection('rules')
            .doc(value.id)
            .update(RuleModelClass(
                    ruleID: value.id,
                    isActive: rule.isActive,
                    isCaseSensitive: rule.isCaseSensitive,
                    ruleName: rule.ruleName,
                    fromNumber: rule.fromNumber,
                    keywords: rule.keywords,
                    isEmail: rule.isEmail,
                    recipients: rule.recipients,
                    method: rule.method,
                    url: rule.url,
                    jsonBody: rule.jsonBody)
                .toJson());
      });
    } catch (e) {
      getErrorSnackBar('A problem has occurred, please try again later');
    }
  }

  updateRuleInList(RuleModelClass rule) async {
    try {
      await firestore
          .collection('users')
          .doc(authController.user!.uid)
          .collection('rules')
          .doc(rule.ruleID)
          .update(rule.toJson());
    } catch (e) {
      getErrorSnackBar('A problem has occurred, please try again later');
    }
  }

  updateForwardAllEmailActiveStatus(
      ForwaredAllEmailModelClass forwaredAllEmailModelClass, bool value) async {
    try {
      await firestore.collection('users').doc(authController.user!.uid).update({
        'forwardAllEmail': ForwaredAllEmailModelClass(
          isActive: value,
          recipients: forwaredAllEmailModelClass.recipients,
        ).toJson()
      });
    } catch (e) {
      getErrorSnackBar('A problem has occurred, please try again later');
    }
  }

  updateForwardAllUrlActiveStatus(
      ForwaredAllURLModelClass forwaredAllURLModelClass, bool value) async {
    try {
      await firestore.collection('users').doc(authController.user!.uid).update({
        'forwardAllUrl': ForwaredAllURLModelClass(
                isActive: value,
                method: forwaredAllURLModelClass.method,
                url: forwaredAllURLModelClass.url,
                jsonBody: forwaredAllURLModelClass.jsonBody)
            .toJson()
      });
    } catch (e) {
      getErrorSnackBar('A problem has occurred, please try again later');
    }
  }

  updateRuleActiveStatus(
      {required RuleModelClass rule, required bool value}) async {
    try {
      await firestore
          .collection('users')
          .doc(authController.user!.uid)
          .collection('rules')
          .doc(rule.ruleID)
          .update({
        "isActive": value,
      });
    } catch (e) {
      getErrorSnackBar('A problem has occurred, please try again later');
    }
  }

  updateForwardAllEmail(
    ForwaredAllEmailModelClass forwaredAllEmailModelClass,
  ) async {
    try {
      await firestore
          .collection('users')
          .doc(authController.user!.uid)
          .update({'forwardAllEmail': forwaredAllEmailModelClass.toJson()});
    } catch (e) {
      getErrorSnackBar('A problem has occurred, please try again later');
    }
  }

  updateForwardAllUrl(
    ForwaredAllURLModelClass forwaredAllURLModelClass,
  ) async {
    try {
      await firestore
          .collection('users')
          .doc(authController.user!.uid)
          .update({'forwardAllUrl': forwaredAllURLModelClass.toJson()});
    } catch (e) {
      getErrorSnackBar('A problem has occurred, please try again later');
    }
  }

  Future<void> deleteAllLogs() async {
    try {
      final logsCollection = firestore
          .collection('users')
          .doc(authController.user!.uid)
          .collection('logs');

      final logsSnapshot = await logsCollection.get();

      final batch = firestore.batch();

      for (final doc in logsSnapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } catch (e) {
      // Handle the error here, e.g., show a snackbar with an error message.
      getErrorSnackBar('A problem has occurred, please try again later');
    }
  }
}
