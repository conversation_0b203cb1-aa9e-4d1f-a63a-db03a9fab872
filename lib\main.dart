import 'package:clarity_flutter/clarity_flutter.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:smsautoforwardapp/services/noti_service.dart';
import 'package:smsautoforwardapp/splash.dart';
import 'package:smsautoforwardapp/style.dart';

// import 'background_service.dart'; // Temporarily commented out
import 'controller/auth_controller.dart';

// 📦 Background handler function (outside main)
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  await dotenv.load();

  Stripe.publishableKey = dotenv.env['STRIPE_PUBLISHABLE_KEY'] ?? '';

  // 🔔 FCM Setup
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  await _requestNotificationPermission();
  _setupNotificationListeners();
  // 👇 Just one line to rule all notifications
  await NotificationService().init();
  // 🔧 Other initializations
  Get.put(AuthController(), permanent: true);
  // startBackgroundService(); // Temporarily commented out - SMS receiver is already registered in AndroidManifest.xml

  runApp(
    const MyApp(),
  );
}

// 🔐 Ask user for notification permission
Future<void> _requestNotificationPermission() async {
  try {
    FirebaseMessaging messaging = FirebaseMessaging.instance;
    await messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );
  } catch (e) {
    //
  }
}

// 🧠 Foreground & opened app notification handlers
void _setupNotificationListeners() {
  FirebaseMessaging.onMessage.listen((RemoteMessage message) {});

  FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {});
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        inputDecorationTheme: InputDecorationTheme(
          fillColor: Colors.white,
          filled: true,
          hintStyle: GoogleFonts.raleway(
            textStyle: const TextStyle(
              fontStyle: FontStyle.normal,
              overflow: TextOverflow.ellipsis,
              color: Colors.grey,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
          disabledBorder: InputBorder.none,
          enabledBorder: InputBorder.none,
        ),
        dropdownMenuTheme: DropdownMenuThemeData(
          menuStyle: MenuStyle(
            backgroundColor: WidgetStateProperty.resolveWith<Color?>(
              (Set<WidgetState> states) {
                if (states.contains(WidgetState.disabled)) {
                  return Colors.grey;
                }
                return Colors.white;
              },
            ),
          ),
          inputDecorationTheme:
              const InputDecorationTheme(border: InputBorder.none),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: maincolor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5.0),
            ),
          ),
        ),
        switchTheme: SwitchThemeData(
          trackOutlineColor: WidgetStateProperty.resolveWith<Color?>(
              (Set<WidgetState> states) {
            return states.contains(WidgetState.selected) ? null : Colors.grey;
          }),
          trackColor: WidgetStateProperty.resolveWith<Color?>(
              (Set<WidgetState> states) {
            return states.contains(WidgetState.selected) ? maincolor : null;
          }),
          thumbColor: WidgetStateProperty.resolveWith<Color?>(
              (Set<WidgetState> states) {
            return states.contains(WidgetState.selected)
                ? Colors.white
                : Colors.grey;
          }),
        ),
        colorScheme: ColorScheme.fromSwatch().copyWith(secondary: maincolor),
        primarySwatch: myCustomPrimarySwatch,
      ),
      home: const Splash(),
    );
  }
}
