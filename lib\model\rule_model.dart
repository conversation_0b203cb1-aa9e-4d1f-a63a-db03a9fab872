import 'package:cloud_firestore/cloud_firestore.dart';

class RuleModelClass {
  String ruleID;
  bool isActive;
  bool isCaseSensitive;
  bool isEmail;
  List<String> recipients;

  String ruleName;
  String fromNumber;
  List<String> keywords;
  String method;
  String url;
  String jsonBody;

  RuleModelClass({
    required this.ruleID,
    required this.isActive,
    required this.isCaseSensitive,
    required this.ruleName,
    required this.fromNumber,
    required this.keywords,
    required this.isEmail,
    required this.recipients,
    required this.method,
    required this.url,
    required this.jsonBody,
  });

  factory RuleModelClass.fromJson(Map<String, dynamic> json) {
    return RuleModelClass(
      ruleID: json['ruleID'],
      isActive: json['isActive'],
      ruleName: json['ruleName'],
      keywords: List<String>.from(json['keywords']),
      fromNumber: json['fromNumber'],
      isCaseSensitive: json['isCaseSensitive'],
      isEmail: json['isEmail'],
      recipients: List<String>.from(json['recipients']),
      method: json['method'],
      url: json['url'],
      jsonBody: json['jsonBody'],
    );
  }

  Map<String, dynamic> toJson() => {
        "ruleID": ruleID,
        "isActive": isActive,
        "ruleName": ruleName,
        "isEmail": isEmail,
        "fromNumber": fromNumber,
        "keywords": keywords,
        "isCaseSensitive": isCaseSensitive,
        "recipients": recipients,
        "url": url,
        "method": method,
        "jsonBody": jsonBody,
      };

  static RuleModelClass fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return RuleModelClass(
      ruleID: snapshot['ruleID'],
      isActive: snapshot['isActive'],
      fromNumber: snapshot['fromNumber'],
      ruleName: snapshot['ruleName'],
      keywords: snapshot['keywords'],
      isCaseSensitive: snapshot['isCaseSensitive'],
      isEmail: snapshot['isEmail'],
      recipients: snapshot['recipients'],
      method: snapshot['method'],
      url: snapshot['url'],
      jsonBody: snapshot['jsonBody'],
    );
  }
}
