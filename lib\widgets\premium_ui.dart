import 'package:get/get.dart';
import 'package:flutter/material.dart' hide ModalBottomSheetRoute;
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:smsautoforwardapp/controller/auth_controller.dart';
import 'package:smsautoforwardapp/controller/payment_controller.dart';
import 'package:smsautoforwardapp/shared/payment_button.dart';
import 'package:smsautoforwardapp/style.dart';

Future<dynamic> premiumBottomSheet(
    {required BuildContext context,
    required bool isModifyingSubscription,
    String? subscriptionId,
    String? subscriptionItem,
    bool forceElitePlan = false}) {
  return showMaterialModalBottomSheet(
    context: context,
    builder: (context) => SingleChildScrollView(
        controller: ModalScrollController.of(context),
        child: SizedBox(
          // height: Get.height * 0.95,
          child: Premium(
            isModifyingSubscription: isModifyingSubscription,
            subscriptionId: subscriptionId,
            subscriptionItem: subscriptionItem,
            forceElitePlan: forceElitePlan,
          ),
        )),
  );
}

class Premium extends StatefulWidget {
  final bool isModifyingSubscription;
  final String? subscriptionId;
  final String? subscriptionItem;
  final bool forceElitePlan;
  final String? stripeCustomerID; // Add stripeCustomerID parameter
  const Premium(
      {super.key,
      required this.isModifyingSubscription,
      this.subscriptionId,
      this.subscriptionItem,
      this.forceElitePlan = false,
      this.stripeCustomerID});

  @override
  State<Premium> createState() => _PremiumState();
}

class _PremiumState extends State<Premium> {
  bool isAnnual = true;
  String amount = '0';
  String planID = '0';
  String planDescription = '0';
  String subscriptionPlan = '0';
  bool isFLiteSelected = false;
  bool isFProSelected = false;
  bool isFSuperSelected = false;
  bool isFEliteSelected = false;
  final AuthController authController = Get.find();
  final PaymentController paymentController = Get.find();
  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, () async {
      if (widget.forceElitePlan) {
        setState(() {
          isFLiteSelected = false;
          isFProSelected = false;
          isFSuperSelected = false;
          isFEliteSelected = true;
          updatePlanDetails();
        });
      } else {
        setState(() {});
      }
    });
  }

  void updatePlanDetails() {
    if (isFLiteSelected) {
      amount = isAnnual ? '11.52' : '1.30';
      planID = isAnnual
          ? 'price_1Pn6LvIl9WGsE0TStaoWsdUT'
          // 'price_1O9HlgIl9WGsE0TSysUtTryX'
          : 'price_1Pn6LvIl9WGsE0TSxdqSyhjf';
      // 'price_1O9HlgIl9WGsE0TSibaYcCDH';
      planDescription = 'Forward up to 750 messages per month';
      subscriptionPlan = 'Lite Plan';
    } else if (isFProSelected) {
      amount = isAnnual ? '47.90' : '4.99';
      planID = isAnnual
          ? 'price_1Pn6NcIl9WGsE0TSAIzbBKsK'
          // 'price_1O9HqEIl9WGsE0TSDkuf6Q2z'
          : 'price_1Pn6NcIl9WGsE0TSVmaJ1mpT';
      // 'price_1O9HqEIl9WGsE0TSWs8y3Tsy';
      planDescription = 'Forward up to 7,500 messages per month';
      subscriptionPlan = 'Pro Plan';
    } else if (isFSuperSelected) {
      amount = isAnnual ? '115.20' : '12.00';
      planID = isAnnual
          ? 'price_1Pn6OPIl9WGsE0TSfiQlVasg'
          // 'price_1O9Hr0Il9WGsE0TSxGtWR5kq'
          : 'price_1Pn6OPIl9WGsE0TSi77aU8H4';
      // 'price_1O9Hr0Il9WGsE0TSwDFfcDNp';
      planDescription = 'Forward up to 65,000 messages per month';
      subscriptionPlan = 'Super Plan';
    } else if (isFEliteSelected) {
      amount = isAnnual ? '211.20' : '22.00';
      planID = isAnnual
          ? 'price_1Pn6PJIl9WGsE0TSnDgGQo9Z'
          // 'price_1O9HsGIl9WGsE0TSktZ6dGF1'
          : 'price_1Pn6OoIl9WGsE0TSa0f5xKZm';
      // 'price_1O9HsGIl9WGsE0TSnrOjYGRr';
      planDescription = 'Forward up to 150,000 messages per month';
      subscriptionPlan = 'Elite Plan';
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        color: bgColor,
        child: Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, bottom: 10),
            child: Column(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        txt(
                          txt: widget.forceElitePlan
                              ? 'Upgrade to Elite Plan'
                              : 'Choose your subscription',
                          fontWeight: FontWeight.bold,
                          fontSize: 24,
                        ),
                        const Icon(
                          Icons.workspace_premium,
                          color: Colors.amber,
                        )
                      ],
                    ),
                    SizedBox(
                      height: Get.height * 0.01,
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    txt(
                      txt: widget.forceElitePlan
                          ? '''To use multiple devices simultaneously, you need the Elite Plan which supports unlimited devices.\n\nUpgrade now to continue using the app on this device.'''
                          : '''Welcome to SMSAutoForwarder - the Guaranteed best priced sms forwarding app! 🎉\n\nFind another better priced app, email us proof and we’ll beat it!\n\nFeel free to cancel or upgrade your subscription at any time.
    ''',
                      maxLines: 10,
                      fontWeight: FontWeight.w500,
                      fontSize: 24,
                    ),
                  ],
                ),
                Row(
                  children: [
                    Switch(
                      // This bool value toggles the switch.
                      value: isAnnual,
                      // thumbColor: const MaterialStatePropertyAll<Color>(Colors.black),
                      onChanged: (bool value) {
                        // This is called when the user toggles the switch.
                        setState(() {
                          isAnnual = value;
                          updatePlanDetails();
                        });
                      },
                    ),
                    txt(
                      txt: isAnnual ? 'Annual billing' : 'Monthly billing',
                      fontSize: 18,
                      // fontWeight: FontWeight.bold,
                      fontColor: blackishColor,
                    ),
                  ],
                ),
                SizedBox(
                  height: Get.height * 0.01,
                ),
                // Only show other plans if not forcing Elite Plan
                if (!widget.forceElitePlan) ...[
                  InkWell(
                    onTap: () {
                      setState(() {
                        isFLiteSelected = true;
                        isFProSelected = false;
                        isFSuperSelected = false;
                        isFEliteSelected = false;
                        updatePlanDetails();
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Container(
                        decoration: BoxDecoration(
                            color: isFLiteSelected
                                ? maincolor.withValues(alpha: 0.1)
                                : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                width: 2,
                                color: isFLiteSelected
                                    ? maincolor
                                    : Colors.transparent)),
                        child: ListTile(
                          title: Padding(
                            padding: const EdgeInsets.only(top: 3, bottom: 3),
                            child: txt(
                              txt: 'Lite Plan',
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              fontColor: blackishColor,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              txt(
                                  txt: 'Forward up to 750 messages per month',
                                  maxLines: 10,
                                  fontWeight: FontWeight.normal,
                                  fontColor: blackishColor,
                                  fontSize: 24),
                              trialText(),
                              Padding(
                                padding:
                                    const EdgeInsets.only(top: 3, bottom: 3),
                                child: txt(
                                    txt:
                                        'then ${isAnnual ? '\$11.52' : '\$1.30'}/${isAnnual ? 'year' : 'month'}',
                                    maxLines: 10,
                                    fontWeight: FontWeight.w600,
                                    fontColor: blackishColor,
                                    fontSize: 24),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      setState(() {
                        isFLiteSelected = false;
                        isFProSelected = true;
                        isFSuperSelected = false;
                        isFEliteSelected = false;
                        updatePlanDetails();
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Container(
                        decoration: BoxDecoration(
                            color: isFProSelected
                                ? maincolor.withValues(alpha: 0.1)
                                : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                width: 2,
                                color: isFProSelected
                                    ? maincolor
                                    : Colors.transparent)),
                        child: ListTile(
                          title: Padding(
                            padding: const EdgeInsets.only(top: 3, bottom: 3),
                            child: txt(
                              txt: 'Pro Plan',
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              fontColor: blackishColor,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              txt(
                                  txt: 'Forward up to 7,500 messages per month',
                                  maxLines: 10,
                                  fontWeight: FontWeight.normal,
                                  fontColor: blackishColor,
                                  fontSize: 24),
                              trialText(),
                              Padding(
                                padding:
                                    const EdgeInsets.only(top: 3, bottom: 3),
                                child: txt(
                                    txt:
                                        'then ${isAnnual ? '\$47.90' : '\$4.99'}/${isAnnual ? 'year' : 'month'}',
                                    maxLines: 10,
                                    fontWeight: FontWeight.w600,
                                    fontColor: blackishColor,
                                    fontSize: 24),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      setState(() {
                        isFLiteSelected = false;
                        isFProSelected = false;
                        isFSuperSelected = true;
                        isFEliteSelected = false;
                        updatePlanDetails();
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Container(
                        decoration: BoxDecoration(
                            color: isFSuperSelected
                                ? maincolor.withValues(alpha: 0.1)
                                : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                width: 2,
                                color: isFSuperSelected
                                    ? maincolor
                                    : Colors.transparent)),
                        child: ListTile(
                          title: Padding(
                            padding: const EdgeInsets.only(top: 3, bottom: 3),
                            child: txt(
                              txt: 'Super Plan',
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              fontColor: blackishColor,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              txt(
                                  txt:
                                      'Forward up to 65,000 messages per month',
                                  maxLines: 10,
                                  fontWeight: FontWeight.normal,
                                  fontColor: blackishColor,
                                  fontSize: 24),
                              trialText(),
                              Padding(
                                padding:
                                    const EdgeInsets.only(top: 3, bottom: 3),
                                child: txt(
                                    txt:
                                        'then ${isAnnual ? '\$115.20' : '\$12.00'}/${isAnnual ? 'year' : 'month'}',
                                    maxLines: 10,
                                    fontWeight: FontWeight.w600,
                                    fontColor: blackishColor,
                                    fontSize: 24),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ], // Close the conditional list for non-Elite plans
                InkWell(
                  onTap: () {
                    setState(() {
                      isFLiteSelected = false;
                      isFProSelected = false;
                      isFSuperSelected = false;
                      isFEliteSelected = true;
                      updatePlanDetails();
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Container(
                      decoration: BoxDecoration(
                          color: isFEliteSelected
                              ? maincolor.withValues(alpha: 0.1)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                              width: 2,
                              color: isFEliteSelected
                                  ? maincolor
                                  : Colors.transparent)),
                      child: ListTile(
                        title: Padding(
                          padding: const EdgeInsets.only(top: 3, bottom: 3),
                          child: txt(
                            txt: 'Elite Plan',
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            fontColor: blackishColor,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            txt(
                                txt: 'Forward up to 150,000 messages per month',
                                maxLines: 10,
                                fontWeight: FontWeight.normal,
                                fontColor: blackishColor,
                                fontSize: 24),
                            trialText(),
                            Padding(
                              padding: const EdgeInsets.only(top: 3, bottom: 3),
                              child: txt(
                                  txt:
                                      'then ${isAnnual ? '\$211.20' : '\$22.00'}/${isAnnual ? 'year' : 'month'}',
                                  maxLines: 10,
                                  fontWeight: FontWeight.w600,
                                  fontColor: blackishColor,
                                  fontSize: 24),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      PaymentButton(
                          buttonTitle: "Subscribe",
                          onPressed: !isFLiteSelected &&
                                  !isFProSelected &&
                                  !isFSuperSelected &&
                                  !isFEliteSelected
                              ? null
                              : () {
                                  if (widget.isModifyingSubscription) {
                                    paymentController.modifySubscription(
                                        newPlanId: planID,
                                        planDescription: planDescription,
                                        subscriptionPlan: subscriptionPlan,
                                        subscriptionId: widget.subscriptionId!,
                                        subscriptionItem:
                                            widget.subscriptionItem!);
                                  } else {
                                    paymentController.init(
                                      amount: amount,
                                      planID: planID,
                                      planDescription: planDescription,
                                      subscriptionPlan: subscriptionPlan,
                                    );
                                  }
                                }),
                    ]),
                SizedBox(
                  height: Get.height * 0.01,
                ),
              ],
            )),
      ),
    );
  }

  Padding trialText() {
    return Padding(
      padding: const EdgeInsets.only(top: 5, bottom: 5),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(3),
          color: Colors.green.shade700,
        ),
        child: Padding(
          padding: const EdgeInsets.all(5.0),
          child: txt(
              txt: '7 days free trial',
              maxLines: 10,
              fontWeight: FontWeight.bold,
              fontColor: Colors.green.shade100,
              fontSize: 24),
        ),
      ),
    );
  }

  Padding iconAndText(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(top: 10),
      child: Row(
        children: [
          Icon(
            icon,
            color: Colors.grey,
          ),
          SizedBox(
            width: Get.width * 0.05,
          ),
          Expanded(child: txt(txt: text, fontSize: 12, maxLines: 3)),
        ],
      ),
    );
  }
}
