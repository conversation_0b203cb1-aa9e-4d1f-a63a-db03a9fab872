import 'package:animated_bottom_navigation_bar/animated_bottom_navigation_bar.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:clarity_flutter/clarity_flutter.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:smsautoforwardapp/controller/auth_controller.dart';
import 'package:smsautoforwardapp/homepage.dart';
import 'package:smsautoforwardapp/profile.dart';
import 'package:smsautoforwardapp/style.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:convert';

import 'bottomsheet_rule.dart';
import 'logs.dart';

class BNB extends StatefulWidget {
  const BNB({
    super.key,
  });

  @override
  State<BNB> createState() => _BNBState();
}

class _BNBState extends State<BNB> with TickerProviderStateMixin {
  final AuthController authController = Get.find();
  final autoSizeGroup = AutoSizeGroup();
  var _bottomNavIndex = 0; //default index of a first screen
  String? userBase36String; // Store the user's base36 string

  final Uri _url =
      Uri.parse('https://smsautoforwarder.com/frequently-asked-questions/');

  final iconList = <IconData>[
    Icons.home,
    Icons.history,
    Icons.help_outline,
    Icons.person,
  ];

  Future<void> _launchUrl() async {
    if (!await canLaunchUrl(Uri.parse(_url.toString()))) {
      throw Exception('Could not launch $_url');
    }
    await launchUrl(Uri.parse(_url.toString()));
  }

  // Convert email to base36 string for Microsoft Clarity
  String _convertEmailToBase36(String email) {
    // Convert email to bytes and then to a number
    List<int> bytes = utf8.encode(email);
    BigInt number = BigInt.zero;

    for (int byte in bytes) {
      number = number * BigInt.from(256) + BigInt.from(byte);
    }

    // Convert to base36
    return number.toRadixString(36);
  }

  // Check and create base36String field if it doesn't exist
  Future<void> _checkAndCreateBase36String() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null || user.email == null) return;

      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;

        if (userData['base36String'] != null) {
          // User already has base36String, use it
          setState(() {
            userBase36String = userData['base36String'];
          });
        } else {
          // Create base36String from email and store it
          final base36String = _convertEmailToBase36(user.email!);

          await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .set({'base36String': base36String}, SetOptions(merge: true));

          setState(() {
            userBase36String = base36String;
          });
        }
      }
    } catch (e) {
      // Handle error silently
    }
  }

  static final List<Widget> _widgetOptions = <Widget>[
    const Homepage(),
    const Logs(),
    Container(),
    const Profile()
  ];

  @override
  void initState() {
    super.initState();
    authController.checkForUpdate();

    // Check subscription expiration as backup to Stripe webhooks
    authController.checkSubscriptionExpiration();

    // Check device restrictions when landing on BNB
    authController.checkDeviceRestrictions();

   

    // Fetch FCM token and update Firestore
    _fetchAndUpdateFCMToken();
  }

  Future<void> _fetchAndUpdateFCMToken() async {
    try {
      // Get FCM token
      String? token = await FirebaseMessaging.instance.getToken();
      if (token != null) {
        // Store token in Firestore
        await _saveFCMTokenToFirestore(token);
      } else {}
    } catch (e) {
      //
    }
  }

  Future<void> _saveFCMTokenToFirestore(String token) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        return;
      }

      // Only update the fcmToken field, leave other fields intact
      await FirebaseFirestore.instance.collection('users').doc(user.uid).set(
          {
            'fcmToken': token, // Only update the fcmToken field
          },
          SetOptions(
              merge: true)); // Merge ensures existing data isn't overwritten
    } catch (e) {
      //
    }
  }

  @override
  Widget build(BuildContext context) {
    // Only show ClarityWidget when we have the base36String
    if (userBase36String == null) {
      return const Scaffold(
        resizeToAvoidBottomInset: false,
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return ClarityWidget(
      clarityConfig: ClarityConfig(
        projectId: "s2707qhfo3",
        userId: userBase36String,
      ),
      app: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: bgColor,
        body: Center(
          child: _widgetOptions.elementAt(_bottomNavIndex),
        ),
        floatingActionButton: Obx(
          () {
            bool hasRestriction = authController.hasRestriction.value;
            return ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(50)),
              child: FloatingActionButton(
                backgroundColor: hasRestriction ? Colors.grey : maincolor,
                onPressed: hasRestriction
                    ? null
                    : () {
                        bottomSheetRule(context, null);
                      },
                child: const Icon(
                  Icons.add,
                  color: Colors.white,
                ),
                //params
              ),
            );
          },
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        bottomNavigationBar: AnimatedBottomNavigationBar(
            icons: iconList,
            activeIndex: _bottomNavIndex,
            gapLocation: GapLocation.center,
            notchSmoothness: NotchSmoothness.smoothEdge,
            onTap: (index) {
              if (index == 2) {
                _launchUrl();
              } else {
                setState(() => _bottomNavIndex = index);
              }
            },
            activeColor: maincolor,
            inactiveColor: lightGrayColor,
            elevation: 5),
      ),
    );
  }
}
