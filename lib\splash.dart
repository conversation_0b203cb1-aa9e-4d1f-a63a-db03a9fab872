import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:smsautoforwardapp/style.dart';

class Splash extends StatefulWidget {
  const Splash({super.key});

  @override
  State<Splash> createState() => _SplashState();
}

class _SplashState extends State<Splash> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
              child: Image.asset(
            'assets/logo.png',
            height: Get.height * 0.2,
          )),
          const SizedBox(
            height: 20,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              txt(
                txt: 'SMS',
                fontColor: blackishColor,
                maxLines: 2,
                fontWeight: FontWeight.w400,
                textAlign: TextAlign.center,
                fontSize: 26,
              ),
              txt(
                txt: 'AutoForwarder',
                fontColor: maincolor,
                maxLines: 2,
                fontWeight: FontWeight.bold,
                textAlign: TextAlign.center,
                fontSize: 26,
              ),
            ],
          )
        ],
      ),
    );
  }
}
